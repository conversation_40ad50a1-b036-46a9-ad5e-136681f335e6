# 任务 2.1：公司信息模块实施计划

## 概述

本文档详细描述了如何实施任务 2.1：模块A - 公司信息 (Business/Company)，将公司信息集成到现有的 `GET /api/v1/advisors/{advisorId}` API 中。

## 目标

在 `AdvisorProfileDTO` 的响应中添加 `companies` 字段，包含顾问关联的所有公司信息，使前端能够渲染公司信息Tab页。

## 数据库分析

### COMPANY 表结构
基于 `sql_schema.txt` 分析，COMPANY 表的关键字段：

```sql
create table SKYTEST.COMPANY (
    COMPANY_INT_ID         NUMBER(8) not null primary key,
    COMPANY_ID             NUMBER(8),
    NAME_EN                VARCHAR2(128),
    NAME_FR                VARCHAR2(128),
    CONTACT                NUMBER(8) references SKYTEST.CONTACT,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    ACTIVE                 VARCHAR2(1),
    COMPANY_TYPE           NUMBER(4),
    ASSIGNABLE_COMMISSIONS VARCHAR2(1),
    PROV_BUSINESS_NUMBER   VARCHAR2(64),
    CONTRACT_EFT           NUMBER(9) references SKYTEST.CONTRACT_EFT,
    BUSINESS_START_DATE    DATE,
    ADVISOR                NUMBER(8),  -- 关联到 ADVISOR 表
    AGENCY                 NUMBER(8) references SKYTEST.AGENCY,
    DELETED                VARCHAR2(1),
    DELETED_DATE           DATE,
    FILE_NAME              VARCHAR2(256),
    PRIMARY_NAME           VARCHAR2(256),
    OTHER_NAME             VARCHAR2(256),
    BUILDING_TYPE          NUMBER(4),
    ONBOARDING             NUMBER(10) references SKYTEST.ONBOARDING_STATUS
)
```

### 关系分析
- `COMPANY.ADVISOR` 字段直接关联到 `ADVISOR.ADVISOR_INT_ID`
- `COMPANY.CONTACT` 字段关联到公司的联系信息（地址等）
- 一个顾问可以关联多个公司（一对多关系）

## 实施步骤

### 子任务 2.1.1: DTO扩展

#### 1. 创建 CompanyDTO 类

**文件位置**: `src/main/java/com/insurfact/ins/dto/CompanyDTO.java`

**设计原则**: 遵循现有架构模式，创建DTO用于API响应，Repository层使用skynet-ejb.jar中的Company实体

**字段设计**:
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "Company information associated with an advisor")
public class CompanyDTO {

    @Schema(description = "Unique company identifier", example = "12345")
    private Long companyId;

    @Schema(description = "Company internal ID", example = "67890")
    private Long companyIntId;

    @Schema(description = "Company name in English", example = "ABC Financial Services Inc.")
    private String nameEn;

    @Schema(description = "Company name in French", example = "Services Financiers ABC Inc.")
    private String nameFr;

    @Schema(description = "Primary company name", example = "ABC Financial")
    private String primaryName;

    @Schema(description = "Other company name", example = "ABC Corp")
    private String otherName;

    @Schema(description = "Provincial business number", example = "123456789BC0001")
    private String provincialBusinessNumber;

    @Schema(description = "Company type code", example = "1")
    private Integer companyType;

    @Schema(description = "Business start date")
    private LocalDate businessStartDate;

    @Schema(description = "Whether company is active", example = "Y")
    private String active;

    @Schema(description = "Whether commissions are assignable", example = "Y")
    private String assignableCommissions;

    @Schema(description = "Building type code", example = "1")
    private Integer buildingType;

    @Schema(description = "Company creation date")
    private LocalDateTime creationDate;

    @Schema(description = "Last modification date")
    private LocalDateTime lastModificationDate;

    @Schema(description = "Whether this is the primary company for the advisor")
    private Boolean isPrimary;

    @Schema(description = "Company contact address information")
    private AddressDTO businessAddress;
}
```

#### 2. 扩展 AdvisorProfileDTO

**修改文件**: `src/main/java/com/insurfact/ins/dto/AdvisorProfileDTO.java`

**添加字段**:
```java
@Schema(description = "List of companies associated with the advisor")
private List<CompanyDTO> companies;
```

**更新 Builder 类**:
- 在 `AdvisorProfileDTOBuilder` 中添加 `companies` 字段
- 添加 `companies(List<CompanyDTO> companies)` 方法
- 在 `build()` 方法中设置 companies 字段

### 子任务 2.1.2: Service层扩展 - 使用Facade模式

**重要发现**: 经过代码分析，发现当前的AdvisorProfileRepository使用直接JDBC查询，这违反了架构规则。根据架构原则，必须使用Facade类进行数据访问。

#### 1. 修改 AdvisorService 注入CompanyFacade

**文件**: `src/main/java/com/insurfact/ins/service/AdvisorService.java`

**原则**: Service层必须使用skynet.ejb包中的Facade类进行所有数据操作，遵循现有架构模式

**注入CompanyFacade**:
```java
import com.insurfact.skynet.ejb.CompanyFacade;
import com.insurfact.skynet.entity.Company;
import jakarta.ejb.EJB;

@Service
@Transactional(readOnly = true)
public class AdvisorService {

    // 现有注入...
    private final AdvisorProfileRepository advisorProfileRepository;
    private final AdvisorMapper advisorMapper;

    // 新增：注入CompanyFacade
    @EJB
    private CompanyFacade companyFacade;

    // 构造函数保持不变...
}
```

#### 2. 创建获取公司数据的Service方法

**基于CompanyFacade分析**: 从`sky_ejb/CompanyFacade.java`中可以看到，没有直接的"根据advisorId获取公司列表"的方法，但我们可以使用JPA查询。

**在AdvisorService中添加方法**:
```java
/**
 * 获取顾问关联的所有公司信息
 * 使用CompanyFacade进行数据访问，遵循Facade模式
 */
private List<Company> getAdvisorCompanies(Integer advisorId) {
    try {
        // 使用CompanyFacade的EntityManager进行查询
        // 基于CompanyFacade中的查询模式
        TypedQuery<Company> query = companyFacade.getEntityManager().createQuery(
            "SELECT c FROM Company c WHERE c.advisor = :advisorId AND (c.deleted IS NULL OR c.deleted != 'Y')",
            Company.class);
        query.setParameter("advisorId", advisorId);
        return query.getResultList();
    } catch (Exception e) {
        log.error("Error fetching companies for advisor {}: {}", advisorId, e.getMessage(), e);
        return new ArrayList<>();
    }
}
```

**注意**: 这种方法需要CompanyFacade提供getEntityManager()方法的访问权限。如果不可用，我们需要在CompanyFacade中添加专门的方法。

#### 3. 替代方案：在CompanyFacade中添加方法

**如果无法直接访问EntityManager，在CompanyFacade中添加方法**:
```java
// 在CompanyFacade中添加
public List<Company> findCompaniesByAdvisor(Integer advisorId) {
    try {
        TypedQuery<Company> query = em.createQuery(
            "SELECT c FROM Company c WHERE c.advisor = :advisorId AND (c.deleted IS NULL OR c.deleted != 'Y')",
            Company.class);
        query.setParameter("advisorId", advisorId);
        return query.getResultList();
    } catch (NoResultException e) {
        return new ArrayList<>();
    }
}
```

**然后在AdvisorService中调用**:
```java
private List<Company> getAdvisorCompanies(Integer advisorId) {
    try {
        return companyFacade.findCompaniesByAdvisor(advisorId);
    } catch (Exception e) {
        log.error("Error fetching companies for advisor {}: {}", advisorId, e.getMessage(), e);
        return new ArrayList<>();
    }
}
```

#### 4. 修改AdvisorService的getAdvisorProfile方法

**集成公司数据到现有流程**:
```java
public AdvisorProfileDTO getAdvisorProfile(Long advisorId) {
    log.debug("Retrieving advisor profile for ID: {}", advisorId);

    // 现有的验证逻辑...
    if (advisorId == null || advisorId <= 0) {
        throw new IllegalArgumentException("Invalid advisor ID");
    }

    try {
        // 1. 获取基础Advisor信息（现有逻辑）
        Advisor advisorEntity = advisorProfileRepository.findProfileById(advisorId);
        if (advisorEntity == null) {
            throw new ResourceNotFoundException("Advisor", advisorId);
        }

        // 2. 通过CompanyFacade获取公司信息（新增）
        List<Company> companies = getAdvisorCompanies(advisorEntity.getAdvisorIntId());

        // 3. 将公司信息设置到Advisor实体（如果支持）
        try {
            advisorEntity.setCompanyList(companies);
        } catch (Exception e) {
            // setCompanyList() 方法可能不存在，我们在Mapper中处理
            log.debug("setCompanyList() method not available, will handle in mapper");
        }

        // 4. 转换为DTO（现有逻辑，但Mapper需要扩展）
        AdvisorProfileDTO advisorProfile = advisorMapper.toAdvisorProfileDTO(advisorEntity, companies);

        // 5. 数据增强（现有逻辑）
        enrichAdvisorProfile(advisorProfile);

        return advisorProfile;

    } catch (ResourceNotFoundException e) {
        throw e;
    } catch (Exception e) {
        log.error("Unexpected error retrieving advisor profile for ID {}: {}", advisorId, e.getMessage(), e);
        throw new RuntimeException("Failed to retrieve advisor profile", e);
    }
}
```
    
    // 新增方法：基于CompanyFacade中的实际方法调用
    private Company buildCompanyEntityFromResultSet(ResultSet rs) throws SQLException {
        Company company = new Company();

        // 基于CompanyFacade中的实际方法调用设置字段
        try {
            company.setCompanyIntId(rs.getInt("COMPANY_INT_ID"));
        } catch (Exception e) {
            // setCompanyIntId() 方法可能不存在，跳过
        }

        try {
            company.setCompanyId(rs.getInt("COMPANY_ID"));
        } catch (Exception e) {
            // setCompanyId() 方法可能不存在，跳过
        }

        // 从CompanyFacade可以看到这些方法存在
        try {
            company.setNameEn(rs.getString("COMPANY_NAME_EN"));
        } catch (Exception e) {
            // setNameEn() 方法可能不存在，跳过
        }

        try {
            company.setNameFr(rs.getString("COMPANY_NAME_FR"));
        } catch (Exception e) {
            // setNameFr() 方法可能不存在，跳过
        }

        try {
            company.setPrimaryName(rs.getString("PRIMARY_NAME"));
        } catch (Exception e) {
            // setPrimaryName() 方法可能不存在，跳过
        }

        try {
            company.setOtherName(rs.getString("OTHER_NAME"));
        } catch (Exception e) {
            // setOtherName() 方法可能不存在，跳过
        }

        try {
            company.setProvBusinessNumber(rs.getString("PROV_BUSINESS_NUMBER"));
        } catch (Exception e) {
            // setProvBusinessNumber() 方法可能不存在，跳过
        }

        try {
            company.setCompanyType(rs.getInt("COMPANY_TYPE"));
        } catch (Exception e) {
            // setCompanyType() 方法可能不存在，跳过
        }

        try {
            company.setBusinessStartDate(rs.getDate("BUSINESS_START_DATE"));
        } catch (Exception e) {
            // setBusinessStartDate() 方法可能不存在，跳过
        }

        try {
            company.setActive(rs.getString("COMPANY_ACTIVE"));
        } catch (Exception e) {
            // setActive() 方法可能不存在，跳过
        }

        try {
            company.setAssignableCommissions(rs.getString("ASSIGNABLE_COMMISSIONS"));
        } catch (Exception e) {
            // setAssignableCommissions() 方法可能不存在，跳过
        }

        try {
            company.setBuildingType(rs.getInt("BUILDING_TYPE"));
        } catch (Exception e) {
            // setBuildingType() 方法可能不存在，跳过
        }

        // 从CompanyFacade可以看到这些方法存在
        try {
            company.setCreationDate(rs.getTimestamp("COMPANY_CREATION_DATE"));
        } catch (Exception e) {
            // setCreationDate() 方法可能不存在，跳过
        }

        try {
            company.setLastModificationDate(rs.getTimestamp("COMPANY_LAST_MODIFICATION_DATE"));
        } catch (Exception e) {
            // setLastModificationDate() 方法可能不存在，跳过
        }

        // 设置公司地址（如果存在）
        if (rs.getInt("COMP_ADDRESS_INT_ID") != 0) {
            Address companyAddress = buildCompanyAddressFromResultSet(rs);
            // 从CompanyFacade可以看到company.contact关系存在
            Contact companyContact = new Contact();
            try {
                companyContact.setAddressList(Arrays.asList(companyAddress));
                company.setContact(companyContact);
            } catch (Exception e) {
                // setContact() 或 setAddressList() 方法可能不存在，跳过
            }
        }

        return company;
    }
    
    private Address buildCompanyAddressFromResultSet(ResultSet rs) throws SQLException {
        Address address = new Address();
        
        address.setAddressIntId(rs.getInt("COMP_ADDRESS_INT_ID"));
        address.setAddressLine1(rs.getString("COMP_ADDRESS_LINE1"));
        address.setAddressLine2(rs.getString("COMP_ADDRESS_LINE2"));
        address.setAddressLine3(rs.getString("COMP_ADDRESS_LINE3"));
        address.setCity(rs.getString("COMP_CITY"));
        address.setPostalCode(rs.getString("COMP_POSTAL_CODE"));
        address.setType(rs.getInt("COMP_ADDRESS_TYPE"));
        address.setCareOf(rs.getString("COMP_CARE_OF"));
        
        // 设置省份和国家
        if (rs.getString("COMP_PROVINCE_CODE") != null) {
            Province province = new Province();
            province.setProvinceCode(rs.getString("COMP_PROVINCE_CODE"));
            province.setNameEn(rs.getString("COMP_PROVINCE_NAME_EN"));
            province.setNameFr(rs.getString("COMP_PROVINCE_NAME_FR"));
            address.setProvince(province);
        }
        
        if (rs.getString("COMP_COUNTRY_CODE") != null) {
            Country country = new Country();
            country.setCountryCode(rs.getString("COMP_COUNTRY_CODE"));
            country.setCountryNameEn(rs.getString("COMP_COUNTRY_NAME_EN"));
            country.setCountryNameFr(rs.getString("COMP_COUNTRY_NAME_FR"));
            address.setCountry(country);
        }
        
        return address;
    }
}
```

### 子任务 2.1.3: Service/Mapper扩展 - 实体到DTO转换

#### 1. 扩展 AdvisorMapper 类

**文件**: `src/main/java/com/insurfact/ins/mapper/AdvisorMapper.java`

**原则**: 遵循现有模式，将skynet Company实体转换为CompanyDTO，与现有的toContactDTO、toAddressDTO等方法保持一致

**修改 toAdvisorProfileDTO 方法**:
```java
public AdvisorProfileDTO toAdvisorProfileDTO(Advisor advisor) {
    if (advisor == null) {
        return null;
    }

    AdvisorProfileDTO.AdvisorProfileDTOBuilder builder = AdvisorProfileDTO.builder()
        // 现有字段...
        .advisorId(convertIntegerToLong(advisor.getAdvisorIntId()))
        .advisorCode(advisor.getAdvisorNumber())
        // ... 其他现有字段

    // 新增：转换公司信息
    if (advisor.getCompanyList() != null) {
        List<CompanyDTO> companies = advisor.getCompanyList().stream()
            .map(this::toCompanyDTO)
            .collect(Collectors.toList());
        builder.companies(companies);
    } else {
        builder.companies(new ArrayList<>());
    }

    // 现有的contact转换逻辑...
    if (advisor.getContact() != null) {
        builder.contact(toContactDTO(advisor.getContact()));
    }

    return builder.build();
}
```

**新增 toCompanyDTO 方法**:
```java
/**
 * Converts a Company entity to CompanyDTO.
 * Follows the same pattern as toContactDTO, toAddressDTO methods.
 */
public CompanyDTO toCompanyDTO(Company company) {
    if (company == null) {
        return null;
    }

    CompanyDTO.CompanyDTOBuilder builder = CompanyDTO.builder()
        .companyId(convertIntegerToLong(company.getCompanyId()))
        .companyIntId(convertIntegerToLong(company.getCompanyIntId()))
        .nameEn(company.getNameEn())
        .nameFr(company.getNameFr())
        .primaryName(company.getPrimaryName())
        .otherName(company.getOtherName())
        .provincialBusinessNumber(company.getProvBusinessNumber())
        .companyType(company.getCompanyType())
        .active(company.getActive())
        .assignableCommissions(company.getAssignableCommissions())
        .buildingType(company.getBuildingType());

    // 转换日期字段
    if (company.getBusinessStartDate() != null) {
        builder.businessStartDate(convertDateToLocalDate(company.getBusinessStartDate()));
    }
    if (company.getCreationDate() != null) {
        builder.creationDate(convertDateToLocalDateTime(company.getCreationDate()));
    }
    if (company.getLastModificationDate() != null) {
        builder.lastModificationDate(convertDateToLocalDateTime(company.getLastModificationDate()));
    }

    // 处理公司地址（通过Company.contact关系）
    if (company.getContact() != null && company.getContact().getAddressList() != null
        && !company.getContact().getAddressList().isEmpty()) {
        // 取第一个地址作为公司地址
        Address businessAddress = company.getContact().getAddressList().get(0);
        builder.businessAddress(toAddressDTO(businessAddress));
    }

    // TODO: 确定主公司标识逻辑
    builder.isPrimary(false); // 默认值，需要根据业务逻辑确定

    return builder.build();
}
```

#### 2. 扩展 AdvisorProfileDTO

**文件**: `src/main/java/com/insurfact/ins/dto/AdvisorProfileDTO.java`

**添加companies字段到Builder**:
```java
@Schema(description = "List of companies associated with the advisor")
private List<CompanyDTO> companies;

// 在AdvisorProfileDTOBuilder中添加
public AdvisorProfileDTOBuilder companies(List<CompanyDTO> companies) {
    this.companies = companies;
    return this;
}

// 在build()方法中添加
public AdvisorProfileDTO build() {
    AdvisorProfileDTO dto = new AdvisorProfileDTO();
    // 现有字段...
    dto.companies = this.companies;
    return dto;
}
```

### 子任务 2.1.4: 测试和验证

#### 1. 单元测试

**创建**: `AdvisorMapperTest.java` 中的公司转换测试
```java
@Test
public void testToCompanyDTO() {
    // 创建测试用的Company实体
    Company company = new Company();
    company.setCompanyIntId(123);
    company.setNameEn("Test Company");
    // ... 设置其他字段

    // 执行转换
    CompanyDTO dto = mapper.toCompanyDTO(company);

    // 验证结果
    assertThat(dto.getCompanyIntId()).isEqualTo(123L);
    assertThat(dto.getNameEn()).isEqualTo("Test Company");
    // ... 其他断言
}
```

#### 2. 集成测试

**修改**: `AdvisorControllerTest.java`
```java
@Test
public void testGetAdvisorProfileWithCompanies() {
    // 测试包含公司信息的顾问档案获取
    // 验证响应中包含companies字段
    // 验证公司数据的完整性
}
```

## 下一步

完成所有子任务后，继续实施任务2.2（执照与责任险模块）

## 风险和注意事项

1. **实体类方法**: skynet-ejb.jar 中的实体类可能没有某些 setter 方法，需要使用 try-catch 处理
2. **查询性能**: 添加 Company JOIN 可能影响查询性能，需要监控
3. **数据完整性**: 确保 COMPANY.DELETED != 'Y' 的过滤条件正确
4. **主公司标识**: 需要确定如何标识主公司（可能需要业务逻辑判断）

## 验收标准

1. `GET /api/v1/advisors/{advisorId}` 响应包含 `companies` 字段
2. `companies` 数组包含顾问关联的所有有效公司
3. 每个公司对象包含完整的公司信息和地址信息
4. API 响应时间不超过现有基准的 150%
5. 所有现有测试继续通过
