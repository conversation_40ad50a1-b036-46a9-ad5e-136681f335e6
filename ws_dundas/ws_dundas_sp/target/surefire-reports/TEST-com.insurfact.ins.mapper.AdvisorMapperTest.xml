<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.insurfact.ins.mapper.AdvisorMapperTest" time="0.064" tests="4" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="23"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/software/workspace/insurfact/ws_dundas/ws_dundas_sp/target/test-classes:/Users/<USER>/software/workspace/insurfact/ws_dundas/ws_dundas_sp/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.2.4/spring-boot-starter-web-3.2.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.4/spring-boot-starter-3.2.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.4/spring-boot-3.2.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.4/spring-boot-autoconfigure-3.2.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.2.4/spring-boot-starter-logging-3.2.4.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.14/logback-classic-1.4.14.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.14/logback-core-1.4.14.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.12/jul-to-slf4j-2.0.12.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.2.4/spring-boot-starter-json-3.2.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.4/jackson-datatype-jdk8-2.15.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.4/jackson-datatype-jsr310-2.15.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.4/jackson-module-parameter-names-2.15.4.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.1.5/spring-web-6.1.5.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.12.4/micrometer-observation-1.12.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.4/micrometer-commons-1.12.4.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.1.5/spring-webmvc-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.1.5/spring-expression-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.2.4/spring-boot-starter-tomcat-3.2.4.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.19/tomcat-embed-core-10.1.19.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.19/tomcat-embed-el-10.1.19.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.19/tomcat-embed-websocket-10.1.19.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.2.4/spring-boot-starter-test-3.2.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.2.4/spring-boot-test-3.2.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.2.4/spring-boot-test-autoconfigure-3.2.4.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.0/json-smart-2.5.0.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.0/accessors-smart-2.5.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.24.2/assertj-core-3.24.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.12/byte-buddy-1.14.12.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.10.2/junit-jupiter-5.10.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.2/junit-jupiter-api-5.10.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.2/junit-platform-commons-1.10.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.2/junit-jupiter-params-5.10.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.2/junit-jupiter-engine-5.10.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.2/junit-platform-engine-1.10.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.7.0/mockito-core-5.7.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.14.12/byte-buddy-agent-1.14.12.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.7.0/mockito-junit-jupiter-5.7.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.1.5/spring-core-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.5/spring-jcl-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.1.5/spring-test-6.1.5.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/Users/<USER>/.m2/repository/jakarta/ejb/jakarta.ejb-api/4.0.1/jakarta.ejb-api-4.0.1.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.12.5/jjwt-0.12.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.5/jjwt-api-0.12.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.5/jjwt-impl-0.12.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.5/jjwt-jackson-0.12.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.4/jackson-databind-2.15.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.4/jackson-annotations-2.15.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.4/jackson-core-2.15.4.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.23.1/log4j-api-2.23.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-core/2.23.1/log4j-core-2.23.1.jar:/Users/<USER>/.m2/repository/org/eclipse/persistence/eclipselink/4.0.2/eclipselink-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/com/sun/xml/bind/jaxb-xjc/4.0.5/jaxb-xjc-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/xml/bind/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/com/insurfact/IQ4Engine/3.1.7/IQ4Engine-3.1.7.jar:/Users/<USER>/.m2/repository/com/insurfact/Entity-JPA/3.1/Entity-JPA-3.1.jar:/Users/<USER>/.m2/repository/org/eclipse/persistence/org.eclipse.persistence.antlr/2.7.14/org.eclipse.persistence.antlr-2.7.14.jar:/Users/<USER>/.m2/repository/org/eclipse/persistence/org.eclipse.persistence.asm/9.7.0/org.eclipse.persistence.asm-9.7.0.jar:/Users/<USER>/.m2/repository/org/eclipse/persistence/org.eclipse.persistence.core/4.0.2/org.eclipse.persistence.core-4.0.2.jar:/Users/<USER>/.m2/repository/org/eclipse/persistence/org.eclipse.persistence.jpa/4.0.2/org.eclipse.persistence.jpa-4.0.2.jar:/Users/<USER>/.m2/repository/org/eclipse/persistence/org.eclipse.persistence.jpa.jpql/4.0.2/org.eclipse.persistence.jpa.jpql-4.0.2.jar:/Users/<USER>/.m2/repository/org/eclipse/persistence/org.eclipse.persistence.jpa.modelgen.processor/4.0.2/org.eclipse.persistence.jpa.modelgen.processor-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/mail/jakarta.mail-api/2.1.3/jakarta.mail-api-2.1.3.jar:/Users/<USER>/.m2/repository/com/oracle/database/jdbc/ojdbc11/********/ojdbc11-********.jar:/Users/<USER>/.m2/repository/com/insurfact/Insurfact-SDK/2.1.5/Insurfact-SDK-2.1.5.jar:/Users/<USER>/.m2/repository/com/google/api-client/google-api-client/2.4.0/google-api-client-2.4.0.jar:/Users/<USER>/.m2/repository/com/google/oauth-client/google-oauth-client/1.35.0/google-oauth-client-1.35.0.jar:/Users/<USER>/.m2/repository/com/google/auth/google-auth-library-credentials/1.23.0/google-auth-library-credentials-1.23.0.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client-gson/1.44.1/google-http-client-gson-1.44.1.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client-apache-v2/1.44.1/google-http-client-apache-v2-1.44.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/Users/<USER>/.m2/repository/com/google/oauth-client/google-oauth-client-jetty/1.35.0/google-oauth-client-jetty-1.35.0.jar:/Users/<USER>/.m2/repository/com/google/oauth-client/google-oauth-client-java6/1.35.0/google-oauth-client-java6-1.35.0.jar:/Users/<USER>/.m2/repository/com/google/apis/google-api-services-gmail/v1-rev110-1.25.0/google-api-services-gmail-v1-rev110-1.25.0.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/client5/httpclient5/5.2.3/httpclient5-5.2.3.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5/5.2.4/httpcore5-5.2.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5-h2/5.2.4/httpcore5-h2-5.2.4.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.16.1/commons-codec-1.16.1.jar:/Users/<USER>/.m2/repository/com/itextpdf/itextpdf/5.5.13.3/itextpdf-5.5.13.3.jar:/Users/<USER>/.m2/repository/com/itextpdf/tool/xmlworker/5.5.13.3/xmlworker-5.5.13.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.1.5/spring-orm-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.1.5/spring-jdbc-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.1.5/spring-tx-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.1.5/spring-beans-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.1.5/spring-context-6.1.5.jar:/Users/<USER>/.m2/repository/com/google/firebase/firebase-admin/9.2.0/firebase-admin-9.2.0.jar:/Users/<USER>/.m2/repository/com/google/api-client/google-api-client-gson/2.2.0/google-api-client-gson-2.2.0.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client/1.43.1/google-http-client-1.43.1.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.18.0/error_prone_annotations-2.18.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar:/Users/<USER>/.m2/repository/io/opencensus/opencensus-contrib-http-util/0.31.1/opencensus-contrib-http-util-0.31.1.jar:/Users/<USER>/.m2/repository/com/google/api/api-common/2.12.0/api-common-2.12.0.jar:/Users/<USER>/.m2/repository/com/google/auto/value/auto-value-annotations/1.10.1/auto-value-annotations-1.10.1.jar:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar:/Users/<USER>/.m2/repository/com/google/auth/google-auth-library-oauth2-http/1.17.0/google-auth-library-oauth2-http-1.17.0.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-storage/2.22.4/google-cloud-storage-2.22.4.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client-jackson2/1.43.1/google-http-client-jackson2-1.43.1.jar:/Users/<USER>/.m2/repository/com/google/apis/google-api-services-storage/v1-rev20230301-2.0.0/google-api-services-storage-v1-rev20230301-2.0.0.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-core/2.19.0/google-cloud-core-2.19.0.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-core-http/2.19.0/google-cloud-core-http-2.19.0.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client-appengine/1.43.1/google-http-client-appengine-1.43.1.jar:/Users/<USER>/.m2/repository/com/google/api/gax-httpjson/0.114.0/gax-httpjson-0.114.0.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-core-grpc/2.19.0/google-cloud-core-grpc-2.19.0.jar:/Users/<USER>/.m2/repository/com/google/api/gax/2.29.0/gax-2.29.0.jar:/Users/<USER>/.m2/repository/com/google/api/gax-grpc/2.29.0/gax-grpc-2.29.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-alts/1.55.1/grpc-alts-1.55.1.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-grpclb/1.55.1/grpc-grpclb-1.55.1.jar:/Users/<USER>/.m2/repository/org/conscrypt/conscrypt-openjdk-uber/2.5.2/conscrypt-openjdk-uber-2.5.2.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-auth/1.55.1/grpc-auth-1.55.1.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-protobuf/1.55.1/grpc-protobuf-1.55.1.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-protobuf-lite/1.55.1/grpc-protobuf-lite-1.55.1.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-context/1.55.1/grpc-context-1.55.1.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-iam-v1/1.15.0/proto-google-iam-v1-1.15.0.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.23.2/protobuf-java-3.23.2.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java-util/3.23.2/protobuf-java-util-3.23.2.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-common-protos/2.20.0/proto-google-common-protos-2.20.0.jar:/Users/<USER>/.m2/repository/org/threeten/threetenbp/1.6.8/threetenbp-1.6.8.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-cloud-storage-v2/2.22.4-alpha/proto-google-cloud-storage-v2-2.22.4-alpha.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/grpc-google-cloud-storage-v2/2.22.4-alpha/grpc-google-cloud-storage-v2-2.22.4-alpha.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/gapic-google-cloud-storage-v2/2.22.4-alpha/gapic-google-cloud-storage-v2-2.22.4-alpha.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-api/1.55.1/grpc-api-1.55.1.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-netty-shaded/1.55.1/grpc-netty-shaded-1.55.1.jar:/Users/<USER>/.m2/repository/io/perfmark/perfmark-api/0.26.0/perfmark-api-0.26.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-core/1.55.1/grpc-core-1.55.1.jar:/Users/<USER>/.m2/repository/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar:/Users/<USER>/.m2/repository/org/codehaus/mojo/animal-sniffer-annotations/1.23/animal-sniffer-annotations-1.23.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-stub/1.55.1/grpc-stub-1.55.1.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-googleapis/1.55.1/grpc-googleapis-1.55.1.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.32.0/checker-qual-3.32.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-xds/1.55.1/grpc-xds-1.55.1.jar:/Users/<USER>/.m2/repository/io/opencensus/opencensus-proto/0.2.0/opencensus-proto-0.2.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-services/1.55.1/grpc-services-1.55.1.jar:/Users/<USER>/.m2/repository/com/google/re2j/re2j/1.6/re2j-1.6.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-rls/1.55.1/grpc-rls-1.55.1.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-firestore/3.13.0/google-cloud-firestore-3.13.0.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-cloud-firestore-v1/3.13.0/proto-google-cloud-firestore-v1-3.13.0.jar:/Users/<USER>/.m2/repository/com/google/cloud/proto-google-cloud-firestore-bundle-v1/3.13.0/proto-google-cloud-firestore-bundle-v1-3.13.0.jar:/Users/<USER>/.m2/repository/io/opencensus/opencensus-contrib-grpc-util/0.31.1/opencensus-contrib-grpc-util-0.31.1.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.107.Final/netty-codec-http-4.1.107.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.107.Final/netty-common-4.1.107.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.107.Final/netty-buffer-4.1.107.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.107.Final/netty-codec-4.1.107.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.107.Final/netty-handler-4.1.107.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.107.Final/netty-resolver-4.1.107.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.107.Final/netty-transport-native-unix-common-4.1.107.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.107.Final/netty-transport-4.1.107.Final.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.0/jaxb-api-2.3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.2.4/spring-boot-starter-validation-3.2.4.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1.Final/hibernate-validator-8.0.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.3.0/springdoc-openapi-starter-webmvc-ui-2.3.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-api/2.3.0/springdoc-openapi-starter-webmvc-api-2.3.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.3.0/springdoc-openapi-starter-common-2.3.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-jakarta/2.2.19/swagger-core-jakarta-2.2.19.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.13.0/commons-lang3-3.13.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations-jakarta/2.2.19/swagger-annotations-jakarta-2.2.19.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models-jakarta/2.2.19/swagger-models-jakarta-2.2.19.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.4/jackson-dataformat-yaml-2.15.4.jar:/Users/<USER>/.m2/repository/org/webjars/swagger-ui/5.10.3/swagger-ui-5.10.3.jar:/Users/<USER>/.m2/repository/org/springframework/hateoas/spring-hateoas/2.3.0/spring-hateoas-2.3.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.1.5/spring-aop-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/3.0.0/spring-plugin-core-3.0.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.12/slf4j-api-2.0.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.2.4/spring-boot-starter-security-3.2.4.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.2.3/spring-security-config-6.2.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.2.3/spring-security-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.2.3/spring-security-crypto-6.2.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.2.3/spring-security-web-6.2.3.jar:"/>
    <property name="java.vm.vendor" value="Homebrew"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="user.timezone" value="America/Toronto"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="23"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CA"/>
    <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/software/workspace/insurfact/ws_dundas/ws_dundas_sp/target/surefire/surefirebooter-20250609193720822_3.jar /Users/<USER>/software/workspace/insurfact/ws_dundas/ws_dundas_sp/target/surefire 2025-06-09T19-37-20_789-jvmRun1 surefire-20250609193720822_1tmp surefire_0-20250609193720822_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="AdvisorMapperTest"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/software/workspace/insurfact/ws_dundas/ws_dundas_sp/target/test-classes:/Users/<USER>/software/workspace/insurfact/ws_dundas/ws_dundas_sp/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.2.4/spring-boot-starter-web-3.2.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.4/spring-boot-starter-3.2.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.4/spring-boot-3.2.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.4/spring-boot-autoconfigure-3.2.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.2.4/spring-boot-starter-logging-3.2.4.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.14/logback-classic-1.4.14.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.14/logback-core-1.4.14.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.12/jul-to-slf4j-2.0.12.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.2.4/spring-boot-starter-json-3.2.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.4/jackson-datatype-jdk8-2.15.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.4/jackson-datatype-jsr310-2.15.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.4/jackson-module-parameter-names-2.15.4.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.1.5/spring-web-6.1.5.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.12.4/micrometer-observation-1.12.4.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.4/micrometer-commons-1.12.4.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.1.5/spring-webmvc-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.1.5/spring-expression-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.2.4/spring-boot-starter-tomcat-3.2.4.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.19/tomcat-embed-core-10.1.19.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.19/tomcat-embed-el-10.1.19.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.19/tomcat-embed-websocket-10.1.19.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.2.4/spring-boot-starter-test-3.2.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.2.4/spring-boot-test-3.2.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.2.4/spring-boot-test-autoconfigure-3.2.4.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.0/json-smart-2.5.0.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.0/accessors-smart-2.5.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.24.2/assertj-core-3.24.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.12/byte-buddy-1.14.12.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.10.2/junit-jupiter-5.10.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.2/junit-jupiter-api-5.10.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.2/junit-platform-commons-1.10.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.2/junit-jupiter-params-5.10.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.2/junit-jupiter-engine-5.10.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.2/junit-platform-engine-1.10.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.7.0/mockito-core-5.7.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.14.12/byte-buddy-agent-1.14.12.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.7.0/mockito-junit-jupiter-5.7.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.1.5/spring-core-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.5/spring-jcl-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.1.5/spring-test-6.1.5.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/Users/<USER>/.m2/repository/jakarta/ejb/jakarta.ejb-api/4.0.1/jakarta.ejb-api-4.0.1.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.12.5/jjwt-0.12.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.5/jjwt-api-0.12.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.5/jjwt-impl-0.12.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.5/jjwt-jackson-0.12.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.4/jackson-databind-2.15.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.4/jackson-annotations-2.15.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.4/jackson-core-2.15.4.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.23.1/log4j-api-2.23.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-core/2.23.1/log4j-core-2.23.1.jar:/Users/<USER>/.m2/repository/org/eclipse/persistence/eclipselink/4.0.2/eclipselink-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/com/sun/xml/bind/jaxb-xjc/4.0.5/jaxb-xjc-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/xml/bind/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/com/insurfact/IQ4Engine/3.1.7/IQ4Engine-3.1.7.jar:/Users/<USER>/.m2/repository/com/insurfact/Entity-JPA/3.1/Entity-JPA-3.1.jar:/Users/<USER>/.m2/repository/org/eclipse/persistence/org.eclipse.persistence.antlr/2.7.14/org.eclipse.persistence.antlr-2.7.14.jar:/Users/<USER>/.m2/repository/org/eclipse/persistence/org.eclipse.persistence.asm/9.7.0/org.eclipse.persistence.asm-9.7.0.jar:/Users/<USER>/.m2/repository/org/eclipse/persistence/org.eclipse.persistence.core/4.0.2/org.eclipse.persistence.core-4.0.2.jar:/Users/<USER>/.m2/repository/org/eclipse/persistence/org.eclipse.persistence.jpa/4.0.2/org.eclipse.persistence.jpa-4.0.2.jar:/Users/<USER>/.m2/repository/org/eclipse/persistence/org.eclipse.persistence.jpa.jpql/4.0.2/org.eclipse.persistence.jpa.jpql-4.0.2.jar:/Users/<USER>/.m2/repository/org/eclipse/persistence/org.eclipse.persistence.jpa.modelgen.processor/4.0.2/org.eclipse.persistence.jpa.modelgen.processor-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/mail/jakarta.mail-api/2.1.3/jakarta.mail-api-2.1.3.jar:/Users/<USER>/.m2/repository/com/oracle/database/jdbc/ojdbc11/********/ojdbc11-********.jar:/Users/<USER>/.m2/repository/com/insurfact/Insurfact-SDK/2.1.5/Insurfact-SDK-2.1.5.jar:/Users/<USER>/.m2/repository/com/google/api-client/google-api-client/2.4.0/google-api-client-2.4.0.jar:/Users/<USER>/.m2/repository/com/google/oauth-client/google-oauth-client/1.35.0/google-oauth-client-1.35.0.jar:/Users/<USER>/.m2/repository/com/google/auth/google-auth-library-credentials/1.23.0/google-auth-library-credentials-1.23.0.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client-gson/1.44.1/google-http-client-gson-1.44.1.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client-apache-v2/1.44.1/google-http-client-apache-v2-1.44.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/Users/<USER>/.m2/repository/com/google/oauth-client/google-oauth-client-jetty/1.35.0/google-oauth-client-jetty-1.35.0.jar:/Users/<USER>/.m2/repository/com/google/oauth-client/google-oauth-client-java6/1.35.0/google-oauth-client-java6-1.35.0.jar:/Users/<USER>/.m2/repository/com/google/apis/google-api-services-gmail/v1-rev110-1.25.0/google-api-services-gmail-v1-rev110-1.25.0.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/client5/httpclient5/5.2.3/httpclient5-5.2.3.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5/5.2.4/httpcore5-5.2.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5-h2/5.2.4/httpcore5-h2-5.2.4.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.16.1/commons-codec-1.16.1.jar:/Users/<USER>/.m2/repository/com/itextpdf/itextpdf/5.5.13.3/itextpdf-5.5.13.3.jar:/Users/<USER>/.m2/repository/com/itextpdf/tool/xmlworker/5.5.13.3/xmlworker-5.5.13.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.1.5/spring-orm-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.1.5/spring-jdbc-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.1.5/spring-tx-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.1.5/spring-beans-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.1.5/spring-context-6.1.5.jar:/Users/<USER>/.m2/repository/com/google/firebase/firebase-admin/9.2.0/firebase-admin-9.2.0.jar:/Users/<USER>/.m2/repository/com/google/api-client/google-api-client-gson/2.2.0/google-api-client-gson-2.2.0.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client/1.43.1/google-http-client-1.43.1.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.18.0/error_prone_annotations-2.18.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar:/Users/<USER>/.m2/repository/io/opencensus/opencensus-contrib-http-util/0.31.1/opencensus-contrib-http-util-0.31.1.jar:/Users/<USER>/.m2/repository/com/google/api/api-common/2.12.0/api-common-2.12.0.jar:/Users/<USER>/.m2/repository/com/google/auto/value/auto-value-annotations/1.10.1/auto-value-annotations-1.10.1.jar:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar:/Users/<USER>/.m2/repository/com/google/auth/google-auth-library-oauth2-http/1.17.0/google-auth-library-oauth2-http-1.17.0.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-storage/2.22.4/google-cloud-storage-2.22.4.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client-jackson2/1.43.1/google-http-client-jackson2-1.43.1.jar:/Users/<USER>/.m2/repository/com/google/apis/google-api-services-storage/v1-rev20230301-2.0.0/google-api-services-storage-v1-rev20230301-2.0.0.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-core/2.19.0/google-cloud-core-2.19.0.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-core-http/2.19.0/google-cloud-core-http-2.19.0.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client-appengine/1.43.1/google-http-client-appengine-1.43.1.jar:/Users/<USER>/.m2/repository/com/google/api/gax-httpjson/0.114.0/gax-httpjson-0.114.0.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-core-grpc/2.19.0/google-cloud-core-grpc-2.19.0.jar:/Users/<USER>/.m2/repository/com/google/api/gax/2.29.0/gax-2.29.0.jar:/Users/<USER>/.m2/repository/com/google/api/gax-grpc/2.29.0/gax-grpc-2.29.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-alts/1.55.1/grpc-alts-1.55.1.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-grpclb/1.55.1/grpc-grpclb-1.55.1.jar:/Users/<USER>/.m2/repository/org/conscrypt/conscrypt-openjdk-uber/2.5.2/conscrypt-openjdk-uber-2.5.2.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-auth/1.55.1/grpc-auth-1.55.1.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-protobuf/1.55.1/grpc-protobuf-1.55.1.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-protobuf-lite/1.55.1/grpc-protobuf-lite-1.55.1.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-context/1.55.1/grpc-context-1.55.1.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-iam-v1/1.15.0/proto-google-iam-v1-1.15.0.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.23.2/protobuf-java-3.23.2.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java-util/3.23.2/protobuf-java-util-3.23.2.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-common-protos/2.20.0/proto-google-common-protos-2.20.0.jar:/Users/<USER>/.m2/repository/org/threeten/threetenbp/1.6.8/threetenbp-1.6.8.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-cloud-storage-v2/2.22.4-alpha/proto-google-cloud-storage-v2-2.22.4-alpha.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/grpc-google-cloud-storage-v2/2.22.4-alpha/grpc-google-cloud-storage-v2-2.22.4-alpha.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/gapic-google-cloud-storage-v2/2.22.4-alpha/gapic-google-cloud-storage-v2-2.22.4-alpha.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-api/1.55.1/grpc-api-1.55.1.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-netty-shaded/1.55.1/grpc-netty-shaded-1.55.1.jar:/Users/<USER>/.m2/repository/io/perfmark/perfmark-api/0.26.0/perfmark-api-0.26.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-core/1.55.1/grpc-core-1.55.1.jar:/Users/<USER>/.m2/repository/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar:/Users/<USER>/.m2/repository/org/codehaus/mojo/animal-sniffer-annotations/1.23/animal-sniffer-annotations-1.23.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-stub/1.55.1/grpc-stub-1.55.1.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-googleapis/1.55.1/grpc-googleapis-1.55.1.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.32.0/checker-qual-3.32.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-xds/1.55.1/grpc-xds-1.55.1.jar:/Users/<USER>/.m2/repository/io/opencensus/opencensus-proto/0.2.0/opencensus-proto-0.2.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-services/1.55.1/grpc-services-1.55.1.jar:/Users/<USER>/.m2/repository/com/google/re2j/re2j/1.6/re2j-1.6.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-rls/1.55.1/grpc-rls-1.55.1.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-firestore/3.13.0/google-cloud-firestore-3.13.0.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-cloud-firestore-v1/3.13.0/proto-google-cloud-firestore-v1-3.13.0.jar:/Users/<USER>/.m2/repository/com/google/cloud/proto-google-cloud-firestore-bundle-v1/3.13.0/proto-google-cloud-firestore-bundle-v1-3.13.0.jar:/Users/<USER>/.m2/repository/io/opencensus/opencensus-contrib-grpc-util/0.31.1/opencensus-contrib-grpc-util-0.31.1.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.107.Final/netty-codec-http-4.1.107.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.107.Final/netty-common-4.1.107.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.107.Final/netty-buffer-4.1.107.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.107.Final/netty-codec-4.1.107.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.107.Final/netty-handler-4.1.107.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.107.Final/netty-resolver-4.1.107.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.107.Final/netty-transport-native-unix-common-4.1.107.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.107.Final/netty-transport-4.1.107.Final.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.0/jaxb-api-2.3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.2.4/spring-boot-starter-validation-3.2.4.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1.Final/hibernate-validator-8.0.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.3.0/springdoc-openapi-starter-webmvc-ui-2.3.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-api/2.3.0/springdoc-openapi-starter-webmvc-api-2.3.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.3.0/springdoc-openapi-starter-common-2.3.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-jakarta/2.2.19/swagger-core-jakarta-2.2.19.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.13.0/commons-lang3-3.13.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations-jakarta/2.2.19/swagger-annotations-jakarta-2.2.19.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models-jakarta/2.2.19/swagger-models-jakarta-2.2.19.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.4/jackson-dataformat-yaml-2.15.4.jar:/Users/<USER>/.m2/repository/org/webjars/swagger-ui/5.10.3/swagger-ui-5.10.3.jar:/Users/<USER>/.m2/repository/org/springframework/hateoas/spring-hateoas/2.3.0/spring-hateoas-2.3.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.1.5/spring-aop-6.1.5.jar:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/3.0.0/spring-plugin-core-3.0.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.12/slf4j-api-2.0.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.2.4/spring-boot-starter-security-3.2.4.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.2.3/spring-security-config-6.2.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.2.3/spring-security-core-6.2.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.2.3/spring-security-crypto-6.2.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.2.3/spring-security-web-6.2.3.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-01-21"/>
    <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/software/workspace/insurfact/ws_dundas/ws_dundas_sp"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/software/workspace/insurfact/ws_dundas/ws_dundas_sp/target/surefire/surefirebooter-20250609193720822_3.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="23.0.2"/>
    <property name="user.name" value="royzhu"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Homebrew"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="java.io.tmpdir" value="/var/folders/cn/cw77tcg159gbdpvznybg_hlm0000gq/T/"/>
    <property name="java.version" value="23.0.2"/>
    <property name="user.dir" value="/Users/<USER>/software/workspace/insurfact/ws_dundas/ws_dundas_sp"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Homebrew"/>
    <property name="java.vm.version" value="23.0.2"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="java.class.version" value="67.0"/>
  </properties>
  <testcase name="testToCompanyDTO_WithBusinessAddress" classname="com.insurfact.ins.mapper.AdvisorMapperTest" time="0.051"/>
  <testcase name="testToCompanyDTO_NullInput" classname="com.insurfact.ins.mapper.AdvisorMapperTest" time="0.001"/>
  <testcase name="testToCompanyDTO_Success" classname="com.insurfact.ins.mapper.AdvisorMapperTest" time="0.003"/>
  <testcase name="testToCompanyDTO_MinimalData" classname="com.insurfact.ins.mapper.AdvisorMapperTest" time="0.001"/>
</testsuite>